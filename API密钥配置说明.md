# AI智能回复API密钥配置说明

## 🎉 优化内容

### 1. AI回复默认启用
- AI智能回复功能现在默认为**开启状态**
- 首次使用时无需手动启用，直接配置API密钥即可使用

### 2. 内置API密钥配置

您现在可以通过两种方式配置DeepSeek API密钥：

#### 方式一：代码内置配置（推荐）
1. 打开 `功能.js` 文件
2. 找到第 **2827行** 左右的 `BUILTIN_API_KEYS` 数组
3. 在数组中添加您的API密钥

```javascript
// ========== 内置API密钥配置区域 ==========
// 在这里直接输入您的DeepSeek API密钥，多个密钥用逗号分隔
// 例如：const BUILTIN_API_KEYS = ['sk-your-key-1', 'sk-your-key-2'];
const BUILTIN_API_KEYS = [
    // 请在下面的引号中输入您的API密钥，可以添加多个
    'sk-your-deepseek-api-key-here',        // 替换为您的第一个API密钥
    'sk-another-api-key-if-needed',         // 可选：添加备用API密钥
];
```

#### 方式二：界面手动添加
1. 打开脚本设置面板
2. 在"AI智能回复"部分找到"API密钥"输入框
3. 输入您的DeepSeek API密钥并点击"保存"

## 🔧 配置步骤

### 获取DeepSeek API密钥
1. 访问 [DeepSeek开放平台](https://platform.deepseek.com/)
2. 注册并登录账户
3. 在API密钥管理页面创建新的API密钥
4. 复制生成的密钥（格式类似：`sk-xxxxxxxxxxxxxxxxxx`）

### 配置密钥到脚本
1. **找到配置位置**：在 `功能.js` 文件的第 **2827行** 左右
2. **替换示例密钥**：将 `'sk-your-deepseek-api-key-here'` 替换为您的真实API密钥
3. **保存文件**：保存修改后重新加载脚本

### 示例配置
```javascript
const BUILTIN_API_KEYS = [
    'sk-1234567890abcdef1234567890abcdef',  // 您的主要API密钥
    'sk-abcdef1234567890abcdef1234567890',  // 备用API密钥（可选）
];
```

## ✅ 验证配置

配置完成后，脚本会自动：
1. 加载内置的API密钥
2. 在控制台显示加载成功信息
3. 在状态监控中显示"已自动加载 X 个内置API密钥"
4. AI回复功能自动启用

## 🔄 密钥管理

- **自动合并**：如果您既配置了内置密钥又通过界面添加了密钥，系统会自动合并
- **去重处理**：重复的密钥会被自动过滤
- **状态监控**：可以在设置面板中查看所有密钥的状态
- **智能切换**：当一个密钥不可用时，系统会自动切换到其他可用密钥

## 📝 注意事项

1. **密钥安全**：请妥善保管您的API密钥，不要分享给他人
2. **多密钥支持**：建议配置多个密钥以提高可用性
3. **格式要求**：确保密钥格式正确（以`sk-`开头）
4. **重新加载**：修改代码后需要重新加载脚本才能生效

## 🆘 常见问题

**Q: 配置后没有生效怎么办？**
A: 请检查密钥格式是否正确，并确保重新加载了脚本

**Q: 可以配置多少个密钥？**
A: 理论上没有限制，建议配置2-3个密钥作为备用

**Q: 如何知道密钥是否有效？**
A: 在设置面板的API密钥状态区域可以查看每个密钥的状态

**Q: 内置密钥和界面添加的密钥有什么区别？**
A: 功能完全相同，内置密钥更方便管理，界面添加更灵活
